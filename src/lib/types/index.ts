import { Database } from './database'

export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row']
export type Inserts<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Insert']
export type Updates<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Update']

// Application Types
export type UserProfile = Tables<'users_profiles'>
export type Client = Tables<'clients'>
export type Project = Tables<'projects'>
export type Invoice = Tables<'invoices'>
export type Activity = Tables<'activities'>

// Extended types with relationships
export type ClientWithProjects = Client & {
  projects?: Project[]
}

export type ProjectWithClient = Project & {
  client?: Client
  assigned_users?: UserProfile[]
}

// Extended Project type with joined data from API calls
export type ProjectWithRelations = Project & {
  client?: {
    id: string
    name: string
    company?: string
    email?: string
    phone?: string
  }
  created_by_user?: {
    id: string
    full_name: string
  }
  assigned_team_members?: {
    id: string
    full_name: string
  }[]
}

export type InvoiceWithClient = Invoice & {
  client?: Client
  project?: Project
}

export type InvoiceWithRelations = Invoice & {
  client?: {
    id: string
    name: string
    company?: string
    email?: string
    phone?: string
    address?: any
  }
  project?: {
    id: string
    name: string
    description?: string
  }
  created_by_user?: {
    id: string
    full_name: string
  }
}

// Form types
export type CreateClientData = Inserts<'clients'>
export type UpdateClientData = Updates<'clients'>
export type CreateProjectData = Inserts<'projects'>
export type UpdateProjectData = Updates<'projects'>
export type CreateInvoiceData = Omit<Inserts<'invoices'>, 'invoice_number'>
export type UpdateInvoiceData = Updates<'invoices'>

// Status types
export type ClientStatus = 'lead' | 'active' | 'inactive' | 'archived'
export type ProjectStatus = 'planning' | 'in_progress' | 'review' | 'completed' | 'cancelled'
export type InvoiceStatus = 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled'
export type UserRole = 'admin' | 'manager' | 'employee'

// Dashboard types
export interface DashboardMetrics {
  totalClients: number
  activeProjects: number
  pendingInvoices: number
  totalRevenue: number
  monthlyRevenue: number
}

export interface ChartData {
  name: string
  value: number
  date?: string
}

// Invoice item type
export interface InvoiceItem {
  id: string
  description: string
  quantity: number
  rate: number
  amount: number
}

// Address type
export interface Address {
  street?: string
  city?: string
  state?: string
  zipCode?: string
  country?: string
}
