export interface CompanySettings {
  id: string
  name: string
  legal_name?: string
  logo_url?: string
  email: string
  phone: string
  website?: string
  
  // Address information
  address: {
    street: string
    city: string
    state: string
    postal_code: string
    country: string
  }
  
  // Indonesian business details
  tax_id?: string // NPWP (Nomor Pokok Wajib Pajak)
  business_license?: string // NIB (Nomor Induk Berusaha)
  company_registration?: string // Akta Pendirian
  
  // Banking information for invoices
  bank_accounts: BankAccount[]
  
  // Invoice settings
  invoice_settings: {
    default_payment_terms: number // days
    payment_terms_text?: string
    late_fee_percentage?: number
    invoice_footer_text?: string
    invoice_notes?: string
  }
  
  // Branding
  brand_colors?: {
    primary: string
    secondary: string
    accent: string
  }
  
  created_at: string
  updated_at: string
}

export interface BankAccount {
  id: string
  bank_name: string
  account_name: string
  account_number: string
  account_type: 'checking' | 'savings' | 'business'
  currency: 'IDR' | 'USD'
  is_primary: boolean
  swift_code?: string // For international transfers
  branch?: string
}

export interface InvoiceTemplate {
  id: string
  name: string
  is_default: boolean
  
  // Layout settings
  layout: {
    show_logo: boolean
    logo_position: 'left' | 'center' | 'right'
    header_style: 'minimal' | 'standard' | 'detailed'
    color_scheme: 'default' | 'professional' | 'modern'
  }
  
  // Content settings
  content: {
    show_company_details: boolean
    show_tax_id: boolean
    show_business_license: boolean
    show_bank_details: boolean
    payment_terms_position: 'header' | 'footer' | 'both'
    notes_position: 'above_items' | 'below_totals' | 'footer'
  }
  
  // Indonesian specific
  indonesian_settings: {
    show_npwp: boolean
    show_nib: boolean
    currency_format: 'symbol' | 'code' | 'both'
    date_format: 'dd/mm/yyyy' | 'dd-mm-yyyy' | 'dd MMM yyyy'
    language: 'id' | 'en' | 'both'
  }
  
  created_at: string
  updated_at: string
}

// Default company settings for new installations
export const DEFAULT_COMPANY_SETTINGS: Partial<CompanySettings> = {
  name: "HarunStudio",
  email: "<EMAIL>",
  phone: "+62 21 1234 5678",
  address: {
    street: "",
    city: "Jakarta",
    state: "DKI Jakarta",
    postal_code: "",
    country: "Indonesia"
  },
  bank_accounts: [],
  invoice_settings: {
    default_payment_terms: 30,
    payment_terms_text: "Payment is due within 30 days of invoice date. Late payments may incur additional charges.",
    invoice_footer_text: "Thank you for your business!",
    invoice_notes: "Please include invoice number in payment reference."
  },
  brand_colors: {
    primary: "#2563eb",
    secondary: "#64748b", 
    accent: "#0ea5e9"
  }
}

// Default invoice template
export const DEFAULT_INVOICE_TEMPLATE: Partial<InvoiceTemplate> = {
  name: "Professional Indonesian",
  is_default: true,
  layout: {
    show_logo: true,
    logo_position: 'left',
    header_style: 'standard',
    color_scheme: 'professional'
  },
  content: {
    show_company_details: true,
    show_tax_id: true,
    show_business_license: true,
    show_bank_details: true,
    payment_terms_position: 'footer',
    notes_position: 'below_totals'
  },
  indonesian_settings: {
    show_npwp: true,
    show_nib: true,
    currency_format: 'symbol',
    date_format: 'dd MMM yyyy',
    language: 'both'
  }
}
