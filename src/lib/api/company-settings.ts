import { createClient as createSupabaseClient } from '@/lib/supabase/client'
import { CompanySettings, BankAccount, InvoiceTemplate, DEFAULT_COMPANY_SETTINGS } from '@/lib/types/company'

export async function getCompanySettings(): Promise<CompanySettings> {
  const supabase = createSupabaseClient()
  
  const { data, error } = await supabase
    .from('company_settings')
    .select('*')
    .single()
  
  if (error) {
    // If no settings exist, return default settings
    if (error.code === 'PGRST116') {
      return DEFAULT_COMPANY_SETTINGS as CompanySettings
    }
    throw new Error(`Failed to fetch company settings: ${error.message}`)
  }
  
  return data
}

export async function updateCompanySettings(settings: Partial<CompanySettings>): Promise<CompanySettings> {
  const supabase = createSupabaseClient()
  
  // First try to update existing settings
  const { data: existingData } = await supabase
    .from('company_settings')
    .select('id')
    .single()
  
  if (existingData) {
    // Update existing settings
    const { data, error } = await supabase
      .from('company_settings')
      .update({
        ...settings,
        updated_at: new Date().toISOString()
      })
      .eq('id', existingData.id)
      .select()
      .single()
    
    if (error) {
      throw new Error(`Failed to update company settings: ${error.message}`)
    }
    
    return data
  } else {
    // Create new settings
    const { data, error } = await supabase
      .from('company_settings')
      .insert({
        ...DEFAULT_COMPANY_SETTINGS,
        ...settings,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()
    
    if (error) {
      throw new Error(`Failed to create company settings: ${error.message}`)
    }
    
    return data
  }
}

export async function uploadCompanyLogo(file: File): Promise<string> {
  const supabase = createSupabaseClient()
  
  // Generate unique filename
  const fileExt = file.name.split('.').pop()
  const fileName = `company-logo-${Date.now()}.${fileExt}`
  
  const { data, error } = await supabase.storage
    .from('company-assets')
    .upload(fileName, file, {
      cacheControl: '3600',
      upsert: false
    })
  
  if (error) {
    throw new Error(`Failed to upload logo: ${error.message}`)
  }
  
  // Get public URL
  const { data: urlData } = supabase.storage
    .from('company-assets')
    .getPublicUrl(data.path)
  
  return urlData.publicUrl
}

export async function getBankAccounts(): Promise<BankAccount[]> {
  const supabase = createSupabaseClient()
  
  const { data, error } = await supabase
    .from('bank_accounts')
    .select('*')
    .order('is_primary', { ascending: false })
    .order('created_at', { ascending: true })
  
  if (error) {
    throw new Error(`Failed to fetch bank accounts: ${error.message}`)
  }
  
  return data || []
}

export async function createBankAccount(bankAccount: Omit<BankAccount, 'id'>): Promise<BankAccount> {
  const supabase = createSupabaseClient()
  
  // If this is set as primary, unset other primary accounts
  if (bankAccount.is_primary) {
    await supabase
      .from('bank_accounts')
      .update({ is_primary: false })
      .eq('is_primary', true)
  }
  
  const { data, error } = await supabase
    .from('bank_accounts')
    .insert(bankAccount)
    .select()
    .single()
  
  if (error) {
    throw new Error(`Failed to create bank account: ${error.message}`)
  }
  
  return data
}

export async function updateBankAccount(id: string, updates: Partial<BankAccount>): Promise<BankAccount> {
  const supabase = createSupabaseClient()
  
  // If this is set as primary, unset other primary accounts
  if (updates.is_primary) {
    await supabase
      .from('bank_accounts')
      .update({ is_primary: false })
      .eq('is_primary', true)
      .neq('id', id)
  }
  
  const { data, error } = await supabase
    .from('bank_accounts')
    .update(updates)
    .eq('id', id)
    .select()
    .single()
  
  if (error) {
    throw new Error(`Failed to update bank account: ${error.message}`)
  }
  
  return data
}

export async function deleteBankAccount(id: string): Promise<void> {
  const supabase = createSupabaseClient()
  
  const { error } = await supabase
    .from('bank_accounts')
    .delete()
    .eq('id', id)
  
  if (error) {
    throw new Error(`Failed to delete bank account: ${error.message}`)
  }
}

export async function getInvoiceTemplates(): Promise<InvoiceTemplate[]> {
  const supabase = createSupabaseClient()
  
  const { data, error } = await supabase
    .from('invoice_templates')
    .select('*')
    .order('is_default', { ascending: false })
    .order('name', { ascending: true })
  
  if (error) {
    throw new Error(`Failed to fetch invoice templates: ${error.message}`)
  }
  
  return data || []
}

export async function getDefaultInvoiceTemplate(): Promise<InvoiceTemplate | null> {
  const supabase = createSupabaseClient()
  
  const { data, error } = await supabase
    .from('invoice_templates')
    .select('*')
    .eq('is_default', true)
    .single()
  
  if (error) {
    if (error.code === 'PGRST116') {
      return null // No default template found
    }
    throw new Error(`Failed to fetch default invoice template: ${error.message}`)
  }
  
  return data
}

// Helper function to get complete invoice data with company settings
export async function getInvoiceWithCompanyData(invoiceId: string) {
  const supabase = createSupabaseClient()
  
  // Get invoice data
  const { data: invoice, error: invoiceError } = await supabase
    .from('invoices')
    .select(`
      *,
      client:clients(id, name, company, email, phone, address),
      project:projects(id, name, description),
      created_by_user:users_profiles!created_by(id, full_name)
    `)
    .eq('id', invoiceId)
    .single()
  
  if (invoiceError) {
    throw new Error(`Failed to fetch invoice: ${invoiceError.message}`)
  }
  
  // Get company settings
  const companySettings = await getCompanySettings()
  
  // Get default template
  const template = await getDefaultInvoiceTemplate()
  
  // Get bank accounts
  const bankAccounts = await getBankAccounts()
  
  return {
    invoice,
    company: companySettings,
    template,
    bankAccounts
  }
}
