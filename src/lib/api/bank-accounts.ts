import { createClient } from '@/utils/supabase/server'
import { BankAccount } from '@/lib/types/company'

export async function getBankAccounts(): Promise<BankAccount[]> {
  const supabase = await createClient()

  const { data, error } = await supabase
    .from('bank_accounts')
    .select('*')
    .order('is_primary', { ascending: false })

  if (error) {
    console.error('Error fetching bank accounts:', error)
    throw new Error(`Failed to fetch bank accounts: ${error.message}`)
  }

  return data || []
}

export async function createBankAccount(bankAccount: Omit<BankAccount, 'id' | 'created_at' | 'updated_at'>): Promise<BankAccount> {
  const supabase = await createClient()

  const { data, error } = await supabase
    .from('bank_accounts')
    .insert([bankAccount])
    .select()
    .single()

  if (error) {
    console.error('Error creating bank account:', error)
    throw new Error(`Failed to create bank account: ${error.message}`)
  }

  return data
}

export async function updateBankAccount(id: string, updates: Partial<BankAccount>): Promise<BankAccount> {
  const supabase = await createClient()

  const { data, error } = await supabase
    .from('bank_accounts')
    .update(updates)
    .eq('id', id)
    .select()
    .single()

  if (error) {
    console.error('Error updating bank account:', error)
    throw new Error(`Failed to update bank account: ${error.message}`)
  }

  return data
}

export async function deleteBankAccount(id: string): Promise<void> {
  const supabase = await createClient()

  const { error } = await supabase
    .from('bank_accounts')
    .delete()
    .eq('id', id)

  if (error) {
    console.error('Error deleting bank account:', error)
    throw new Error(`Failed to delete bank account: ${error.message}`)
  }
}
