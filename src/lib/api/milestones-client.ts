import { createClient as createSupabaseClient } from '@/lib/supabase/client'
import { CreateMilestonesData, PaymentMilestoneData } from '@/lib/validations'
import { createMilestoneInvoice, validateProjectBudget } from './invoices-client'
import { getCurrentUserIdOrFallback } from '@/lib/auth'

export interface MilestoneInvoice {
  id: string
  invoice_number: string
  amount: number
  total_amount: number
  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled'
  milestone_type: 'dp' | 'progress' | 'final'
  milestone_percentage: number
  sequence_number: number
  due_date: string | null
  paid_date: string | null
  created_at: string
}

export interface ProjectMilestonesSummary {
  project_id: string
  project_name: string
  project_budget: number | null
  total_invoiced: number
  remaining_budget: number
  milestones: MilestoneInvoice[]
  completion_percentage: number
}

export async function createProjectMilestones(data: CreateMilestonesData) {
  const supabase = createSupabaseClient()
  const currentUserId = await getCurrentUserIdOrFallback()
  
  // Get project and client information
  const { data: project, error: projectError } = await supabase
    .from('projects')
    .select('id, name, budget, client_id')
    .eq('id', data.project_id)
    .single()
  
  if (projectError) {
    throw new Error(`Failed to fetch project: ${projectError.message}`)
  }
  
  if (!project.budget) {
    throw new Error('Project must have a budget to create payment milestones')
  }
  
  // Validate that milestones don't exceed budget
  const totalMilestoneAmount = data.milestones.reduce((sum, milestone) => {
    return sum + (project.budget * milestone.percentage / 100)
  }, 0)
  
  if (totalMilestoneAmount > project.budget) {
    throw new Error('Total milestone amounts exceed project budget')
  }
  
  // Create milestone invoices
  const createdInvoices = []
  
  for (let i = 0; i < data.milestones.length; i++) {
    const milestone = data.milestones[i]
    const amount = Math.round(project.budget * milestone.percentage / 100)
    
    const invoice = await createMilestoneInvoice({
      project_id: data.project_id,
      client_id: project.client_id,
      milestone_type: milestone.type,
      milestone_percentage: milestone.percentage,
      sequence_number: i + 1,
      amount,
      description: milestone.description,
      due_date: milestone.due_date,
      created_by: currentUserId,
    })
    
    createdInvoices.push(invoice)
  }
  
  return createdInvoices
}

export async function getProjectMilestonesSummary(projectId: string): Promise<ProjectMilestonesSummary> {
  const supabase = createSupabaseClient()
  
  // Get project information
  const { data: project, error: projectError } = await supabase
    .from('projects')
    .select('id, name, budget')
    .eq('id', projectId)
    .single()
  
  if (projectError) {
    throw new Error(`Failed to fetch project: ${projectError.message}`)
  }
  
  // Get milestone invoices
  const { data: milestones, error: milestonesError } = await supabase
    .from('invoices')
    .select(`
      id,
      invoice_number,
      amount,
      total_amount,
      status,
      milestone_type,
      milestone_percentage,
      sequence_number,
      due_date,
      paid_date,
      created_at
    `)
    .eq('project_id', projectId)
    .neq('milestone_type', 'standard')
    .order('sequence_number', { ascending: true })
  
  if (milestonesError) {
    throw new Error(`Failed to fetch milestones: ${milestonesError.message}`)
  }
  
  // Calculate totals
  const totalInvoiced = milestones.reduce((sum, milestone) => sum + milestone.total_amount, 0)
  const remainingBudget = (project.budget || 0) - totalInvoiced
  const paidMilestones = milestones.filter(m => m.status === 'paid')
  const completionPercentage = milestones.length > 0 
    ? Math.round((paidMilestones.length / milestones.length) * 100)
    : 0
  
  return {
    project_id: project.id,
    project_name: project.name,
    project_budget: project.budget,
    total_invoiced: totalInvoiced,
    remaining_budget: remainingBudget,
    milestones: milestones as MilestoneInvoice[],
    completion_percentage: completionPercentage,
  }
}

export async function updateMilestoneStatus(invoiceId: string, status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled', paidDate?: string) {
  const supabase = createSupabaseClient()
  
  const updateData: any = { status }
  if (status === 'paid' && paidDate) {
    updateData.paid_date = paidDate
  } else if (status !== 'paid') {
    updateData.paid_date = null
  }
  
  const { data, error } = await supabase
    .from('invoices')
    .update(updateData)
    .eq('id', invoiceId)
    .select()
    .single()
  
  if (error) {
    throw new Error(`Failed to update milestone status: ${error.message}`)
  }
  
  return data
}

export async function deleteMilestone(invoiceId: string) {
  const supabase = createSupabaseClient()
  
  const { error } = await supabase
    .from('invoices')
    .delete()
    .eq('id', invoiceId)
  
  if (error) {
    throw new Error(`Failed to delete milestone: ${error.message}`)
  }
}

export async function canCreateMilestones(projectId: string): Promise<{ canCreate: boolean; reason?: string }> {
  const supabase = createSupabaseClient()
  
  // Check if project has budget
  const { data: project, error: projectError } = await supabase
    .from('projects')
    .select('budget')
    .eq('id', projectId)
    .single()
  
  if (projectError) {
    return { canCreate: false, reason: 'Project not found' }
  }
  
  if (!project.budget) {
    return { canCreate: false, reason: 'Project must have a budget to create payment milestones' }
  }
  
  // Check if milestones already exist
  const { data: existingMilestones, error: milestonesError } = await supabase
    .from('invoices')
    .select('id')
    .eq('project_id', projectId)
    .neq('milestone_type', 'standard')
    .limit(1)
  
  if (milestonesError) {
    return { canCreate: false, reason: 'Error checking existing milestones' }
  }
  
  if (existingMilestones.length > 0) {
    return { canCreate: false, reason: 'Payment milestones already exist for this project' }
  }
  
  return { canCreate: true }
}
