import { createClient } from '@/utils/supabase/server'
import { InvoiceWithRelations } from '@/lib/types'
import { CompanySettings, BankAccount, InvoiceTemplate } from '@/lib/types/company'

export interface InvoicePDFData {
  invoice: InvoiceWithRelations
  company: CompanySettings
  template: InvoiceTemplate | null
  bankAccounts: BankAccount[]
}

export async function getInvoiceWithCompanyData(invoiceId: string): Promise<InvoicePDFData> {
  const supabase = await createClient()

  try {
    // Get invoice with related data
    const { data: invoice, error: invoiceError } = await supabase
      .from('invoices')
      .select(`
        *,
        client:clients(*),
        project:projects(*)
      `)
      .eq('id', invoiceId)
      .single()

    if (invoiceError) {
      console.error('Error fetching invoice:', invoiceError)
      throw new Error(`Failed to fetch invoice: ${invoiceError.message}`)
    }

    if (!invoice) {
      throw new Error('Invoice not found')
    }

    // Get company settings
    const { data: company, error: companyError } = await supabase
      .from('company_settings')
      .select('*')
      .single()

    if (companyError) {
      console.error('Error fetching company settings:', companyError)
      throw new Error(`Failed to fetch company settings: ${companyError.message}`)
    }

    // Get bank accounts
    const { data: bankAccounts, error: bankError } = await supabase
      .from('bank_accounts')
      .select('*')
      .order('is_primary', { ascending: false })

    if (bankError) {
      console.error('Error fetching bank accounts:', bankError)
      // Don't throw error for bank accounts as they're optional
    }

    // Get invoice template (optional)
    const { data: template, error: templateError } = await supabase
      .from('invoice_templates')
      .select('*')
      .eq('is_default', true)
      .single()

    if (templateError && templateError.code !== 'PGRST116') {
      console.error('Error fetching template:', templateError)
      // Don't throw error for template as it's optional
    }

    return {
      invoice: invoice as InvoiceWithRelations,
      company: company as CompanySettings,
      template: template as InvoiceTemplate | null,
      bankAccounts: (bankAccounts || []) as BankAccount[]
    }
  } catch (error) {
    console.error('Error in getInvoiceWithCompanyData:', error)
    throw error
  }
}
