"use client"

import React from "react"
import Image from "next/image"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { formatInvoiceCurrency } from "@/lib/utils/currency"
import { InvoiceWithRelations, InvoiceItem } from "@/lib/types"
import { CompanySettings, BankAccount, InvoiceTemplate } from "@/lib/types/company"
import { format } from "date-fns"
import { id as idLocale } from "date-fns/locale"

// Type guard for invoice items
function isInvoiceItemArray(items: unknown): items is InvoiceItem[] {
  return Array.isArray(items) && items.every(item =>
    typeof item === 'object' &&
    item !== null &&
    'description' in item &&
    'quantity' in item &&
    'rate' in item &&
    'amount' in item
  )
}

interface ProfessionalInvoiceLayoutProps {
  invoice: InvoiceWithRelations
  company: CompanySettings
  template?: InvoiceTemplate | null
  bankAccounts: BankAccount[]
  className?: string
  isPrintMode?: boolean
}

export function ProfessionalInvoiceLayout({
  invoice,
  company,
  template,
  bankAccounts,
  className = "",
  isPrintMode = false
}: ProfessionalInvoiceLayoutProps) {
  const primaryBankAccount = bankAccounts.find(account => 
    account.is_primary && account.currency === invoice.currency
  ) || bankAccounts.find(account => account.currency === invoice.currency) || bankAccounts[0]

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    if (template?.indonesian_settings?.date_format === 'dd/mm/yyyy') {
      return format(date, 'dd/MM/yyyy')
    } else if (template?.indonesian_settings?.date_format === 'dd-mm-yyyy') {
      return format(date, 'dd-MM-yyyy')
    }
    return format(date, 'dd MMM yyyy', { locale: idLocale })
  }

  const showIndonesian = template?.indonesian_settings?.language === 'id' || template?.indonesian_settings?.language === 'both'
  const showEnglish = template?.indonesian_settings?.language === 'en' || template?.indonesian_settings?.language === 'both'

  return (
    <div className={`bg-white ${isPrintMode ? 'print-layout' : ''} ${className}`}>
      {/* Company Header */}
      <div className="invoice-header mb-8">
        <div className="flex items-start justify-between">
          {/* Company Logo and Info */}
          <div className="flex items-start gap-6">
            {template?.layout?.show_logo && company.logo_url && (
              <div className="company-logo">
                <Image
                  src={company.logo_url}
                  alt={company.name}
                  width={200}
                  height={80}
                  className="h-16 w-auto object-contain"
                />
              </div>
            )}
            
          </div>
          
          {/* Invoice Title and Number */}
          <div className="text-right">
            <h2 className="text-3xl font-bold text-gray-900 mb-2">
              {showIndonesian && showEnglish ? 'INVOICE / FAKTUR' : 
               showIndonesian ? 'FAKTUR' : 'INVOICE'}
            </h2>
            <div className="text-lg font-semibold text-gray-700 mb-1">
              #{invoice.invoice_number}
            </div>
            {invoice.milestone_type && invoice.milestone_type !== 'standard' && (
              <Badge variant="secondary" className="mb-2">
                {invoice.milestone_type === 'dp' && (showIndonesian ? 'Uang Muka' : 'Down Payment')}
                {invoice.milestone_type === 'progress' && (showIndonesian ? 'Pembayaran Bertahap' : 'Progress Payment')}
                {invoice.milestone_type === 'final' && (showIndonesian ? 'Pembayaran Akhir' : 'Final Payment')}
                {invoice.milestone_percentage && ` (${invoice.milestone_percentage}%)`}
              </Badge>
            )}
          </div>
        </div>
      </div>

      <Separator className="my-6" />

      {/* Invoice Details and Client Info */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
        {/* Bill To */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-3">
            {showIndonesian && showEnglish ? 'Bill To / Tagihan Kepada' :
             showIndonesian ? 'Tagihan Kepada' : 'Bill To'}
          </h3>
          <div className="text-sm space-y-1">
            <p className="font-semibold text-gray-900">{invoice.client?.name}</p>
            {invoice.client?.company && (
              <p className="text-gray-700">{invoice.client.company}</p>
            )}
            {invoice.client?.address && (
              <div className="text-gray-600">
                {typeof invoice.client.address === 'object' && (
                  <>
                    <p>{invoice.client.address.street}</p>
                    <p>{invoice.client.address.city}, {invoice.client.address.state}</p>
                    <p>{invoice.client.address.postal_code}</p>
                  </>
                )}
              </div>
            )}
            {invoice.client?.email && (
              <p className="text-gray-600">Email: {invoice.client.email}</p>
            )}
            {invoice.client?.phone && (
              <p className="text-gray-600">Phone: {invoice.client.phone}</p>
            )}
          </div>
        </div>

        {/* Invoice Details */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-3">
            {showIndonesian && showEnglish ? 'Invoice Details / Detail Faktur' :
             showIndonesian ? 'Detail Faktur' : 'Invoice Details'}
          </h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">
                {showIndonesian ? 'Tanggal Faktur:' : 'Invoice Date:'}
              </span>
              <span className="font-medium text-gray-600">{formatDate(invoice.created_at)}</span>
            </div>
            {invoice.due_date && (
              <div className="flex justify-between">
                <span className="font-medium text-gray-600">
                  {showIndonesian ? 'Jatuh Tempo:' : 'Due Date:'}
                </span>
                <span className="font-medium">{formatDate(invoice.due_date)}</span>
              </div>
            )}
            {invoice.project && (
              <div className="flex justify-between">
                <span className="font-medium text-gray-600">
                  {showIndonesian ? 'Proyek:' : 'Project:'}
                </span>
                <span className="font-medium text-gray-600">{invoice.project.name}</span>
              </div>
            )}
            <div className="flex justify-between">
              <span className="text-gray-600">
                {showIndonesian ? 'Mata Uang:' : 'Currency:'}
              </span>
              <span className="font-medium text-gray-600">{invoice.currency}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Invoice Items Table */}
      <div className="mb-8">
        <div className="border border-gray-200 rounded-lg overflow-hidden">
          {/* Table Header */}
          <div className="bg-gray-50 px-6 py-3 border-b border-gray-200">
            <div className="grid grid-cols-12 gap-4 text-sm font-semibold text-gray-900">
              <div className="col-span-6">
                {showIndonesian && showEnglish ? 'Description / Deskripsi' :
                 showIndonesian ? 'Deskripsi' : 'Description'}
              </div>
              <div className="col-span-2 text-center">
                {showIndonesian ? 'Jumlah' : 'Qty'}
              </div>
              <div className="col-span-2 text-center">
                {showIndonesian ? 'Harga' : 'Rate'}
              </div>
              <div className="col-span-2 text-right">
                {showIndonesian ? 'Total' : 'Amount'}
              </div>
            </div>
          </div>
          
          {/* Table Body */}
          <div className="divide-y divide-gray-200">
            {invoice.items && isInvoiceItemArray(invoice.items) && invoice.items.map((item, index) => (
              <div key={index} className="px-6 py-4">
                <div className="grid grid-cols-12 gap-4 text-sm">
                  <div className="col-span-6 text-gray-900">{item.description}</div>
                  <div className="col-span-2 text-center text-gray-700">{item.quantity}</div>
                  <div className="col-span-2 text-center text-gray-700">
                    {formatInvoiceCurrency(item.rate, invoice.currency as 'IDR' | 'USD')}
                  </div>
                  <div className="col-span-2 text-right font-medium text-gray-900">
                    {formatInvoiceCurrency(item.amount, invoice.currency as 'IDR' | 'USD')}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Totals Section */}
      <div className="flex justify-end mb-8">
        <div className="w-full max-w-sm">
          <div className="space-y-2 text-sm">
            <div className="flex justify-between py-2">
              <span className="text-gray-600">
                {showIndonesian ? 'Subtotal:' : 'Subtotal:'}
              </span>
              <span className="font-medium">
                {formatInvoiceCurrency(invoice.amount, invoice.currency as 'IDR' | 'USD')}
              </span>
            </div>

            {invoice.tax_amount > 0 && (
              <div className="flex justify-between py-2">
                <span className="text-gray-600">
                  {showIndonesian ? 'Pajak:' : 'Tax:'}
                </span>
                <span className="font-medium">
                  {formatInvoiceCurrency(invoice.tax_amount, invoice.currency as 'IDR' | 'USD')}
                </span>
              </div>
            )}

            <Separator />

            <div className="flex justify-between py-3 text-lg font-bold">
              <span className="text-gray-900">
                {showIndonesian ? 'Total:' : 'Total:'}
              </span>
              <span className="text-gray-900">
                {formatInvoiceCurrency(invoice.total_amount, invoice.currency as 'IDR' | 'USD')}
              </span>
            </div>
          </div>
        </div>
      </div>



      {/* Invoice Notes (only if specified on the invoice) */}
      {invoice.notes && (
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            {showIndonesian ? 'Catatan' : 'Notes'}
          </h3>
          <p className="text-sm text-gray-600">{invoice.notes}</p>
        </div>
      )}

      {/* Invoice Footer */}
      <div className="mt-12 pt-6 border-t border-gray-200">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Company Information */}
          <div>
            <h4 className="font-semibold text-gray-900 mb-3">
              {showIndonesian && showEnglish ? 'Company Information / Informasi Perusahaan' :
               showIndonesian ? 'Informasi Perusahaan' : 'Company Information'}
            </h4>
            <div className="text-sm text-gray-600 space-y-1">
              <p className="font-medium text-gray-900">{company.name}</p>
              {company.legal_name && company.legal_name !== company.name && (
                <p>{company.legal_name}</p>
              )}
              <p>{company.address.street}</p>
              <p>{company.address.city}, {company.address.state} {company.address.postal_code}</p>
              <p>{company.address.country}</p>
              <p>Email: {company.email}</p>
              <p>Phone: {company.phone}</p>
              {company.website && <p>Website: {company.website}</p>}

              {/* Indonesian Business Details */}
              {template?.content?.show_tax_id && company.tax_id && (
                <p>NPWP: {company.tax_id}</p>
              )}
              {template?.content?.show_business_license && company.business_license && (
                <p>NIB: {company.business_license}</p>
              )}
            </div>
          </div>

          {/* Payment Information */}
          {template?.content?.show_bank_details && primaryBankAccount && (
            <div>
              <h4 className="font-semibold text-gray-900 mb-3">
                {showIndonesian && showEnglish ? 'Payment Information / Informasi Pembayaran' :
                 showIndonesian ? 'Informasi Pembayaran' : 'Payment Information'}
              </h4>
              <div className="text-sm text-gray-600 space-y-1">
                <p className="font-medium text-gray-900">{primaryBankAccount.bank_name}</p>
                <p>
                  {showIndonesian ? 'Nama Rekening:' : 'Account Name:'} {primaryBankAccount.account_name}
                </p>
                <p>
                  {showIndonesian ? 'Nomor Rekening:' : 'Account Number:'} {primaryBankAccount.account_number}
                </p>
                <p>
                  {showIndonesian ? 'Mata Uang:' : 'Currency:'} {primaryBankAccount.currency}
                </p>
                {primaryBankAccount.swift_code && (
                  <p>SWIFT: {primaryBankAccount.swift_code}</p>
                )}
                {primaryBankAccount.branch && (
                  <p>
                    {showIndonesian ? 'Cabang:' : 'Branch:'} {primaryBankAccount.branch}
                  </p>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Footer Text */}
        {company.invoice_settings?.invoice_footer_text && (
          <div className="text-center mt-6 pt-4 border-t border-gray-100">
            <p className="text-sm text-gray-500">
              {company.invoice_settings.invoice_footer_text}
            </p>
          </div>
        )}
      </div>
    </div>
  )
}
