"use client"

import React, { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Loader2, Plus, Edit, Trash2, CreditCard, Building2 } from "lucide-react"
import { getBankAccounts, createBankAccount, updateBankAccount, deleteBankAccount } from "@/lib/api/company-settings"
import { BankAccount } from "@/lib/types/company"

const bankAccountSchema = z.object({
  bank_name: z.string().min(1, "Bank name is required"),
  account_name: z.string().min(1, "Account name is required"),
  account_number: z.string().min(1, "Account number is required"),
  account_type: z.enum(["checking", "savings", "business"]),
  currency: z.enum(["IDR", "USD"]),
  is_primary: z.boolean().default(false),
  swift_code: z.string().optional(),
  branch: z.string().optional(),
})

type BankAccountFormData = z.infer<typeof bankAccountSchema>

export function BankAccountManager() {
  const [bankAccounts, setBankAccounts] = useState<BankAccount[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingAccount, setEditingAccount] = useState<BankAccount | null>(null)

  const form = useForm<BankAccountFormData>({
    resolver: zodResolver(bankAccountSchema),
    defaultValues: {
      bank_name: "",
      account_name: "",
      account_number: "",
      account_type: "business",
      currency: "IDR",
      is_primary: false,
      swift_code: "",
      branch: "",
    },
  })

  useEffect(() => {
    loadBankAccounts()
  }, [])

  const loadBankAccounts = async () => {
    setLoading(true)
    try {
      const accounts = await getBankAccounts()
      setBankAccounts(accounts)
    } catch (error) {
      console.error("Failed to load bank accounts:", error)
    } finally {
      setLoading(false)
    }
  }

  const onSubmit = async (data: BankAccountFormData) => {
    setSaving(true)
    try {
      if (editingAccount) {
        await updateBankAccount(editingAccount.id, data)
      } else {
        await createBankAccount(data)
      }
      
      await loadBankAccounts()
      handleCloseForm()
    } catch (error) {
      console.error("Failed to save bank account:", error)
      alert("Failed to save bank account. Please try again.")
    } finally {
      setSaving(false)
    }
  }

  const handleEdit = (account: BankAccount) => {
    setEditingAccount(account)
    form.reset({
      bank_name: account.bank_name,
      account_name: account.account_name,
      account_number: account.account_number,
      account_type: account.account_type,
      currency: account.currency,
      is_primary: account.is_primary,
      swift_code: account.swift_code || "",
      branch: account.branch || "",
    })
    setShowAddForm(true)
  }

  const handleDelete = async (accountId: string) => {
    if (!confirm("Are you sure you want to delete this bank account?")) {
      return
    }

    try {
      await deleteBankAccount(accountId)
      await loadBankAccounts()
    } catch (error) {
      console.error("Failed to delete bank account:", error)
      alert("Failed to delete bank account. Please try again.")
    }
  }

  const handleCloseForm = () => {
    setShowAddForm(false)
    setEditingAccount(null)
    form.reset()
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin mr-2" />
          <span>Loading bank accounts...</span>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5" />
              Bank Accounts
            </CardTitle>
            <CardDescription>
              Manage bank accounts for invoice payments
            </CardDescription>
          </div>
          <Dialog open={showAddForm} onOpenChange={setShowAddForm}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Add Bank Account
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>
                  {editingAccount ? 'Edit Bank Account' : 'Add Bank Account'}
                </DialogTitle>
                <DialogDescription>
                  {editingAccount ? 'Update bank account information' : 'Add a new bank account for invoice payments'}
                </DialogDescription>
              </DialogHeader>

              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                  <FormField
                    control={form.control}
                    name="bank_name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Bank Name *</FormLabel>
                        <FormControl>
                          <Input placeholder="Bank Central Asia (BCA)" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="account_name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Account Name *</FormLabel>
                        <FormControl>
                          <Input placeholder="PT HarunStudio Indonesia" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="account_number"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Account Number *</FormLabel>
                        <FormControl>
                          <Input placeholder="**********" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="account_type"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Account Type</FormLabel>
                          <Select onValueChange={field.onChange} value={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="business">Business</SelectItem>
                              <SelectItem value="checking">Checking</SelectItem>
                              <SelectItem value="savings">Savings</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="currency"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Currency</FormLabel>
                          <Select onValueChange={field.onChange} value={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="IDR">IDR</SelectItem>
                              <SelectItem value="USD">USD</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="swift_code"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>SWIFT Code (Optional)</FormLabel>
                        <FormControl>
                          <Input placeholder="CENAIDJA" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="branch"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Branch (Optional)</FormLabel>
                        <FormControl>
                          <Input placeholder="Jakarta Pusat" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="flex items-center space-x-2">
                    <FormField
                      control={form.control}
                      name="is_primary"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                          <FormControl>
                            <input
                              type="checkbox"
                              checked={field.value}
                              onChange={field.onChange}
                              className="mt-1"
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>Set as primary account</FormLabel>
                            <p className="text-xs text-muted-foreground">
                              Primary accounts are shown first on invoices
                            </p>
                          </div>
                        </FormItem>
                      )}
                    />
                  </div>

                  <DialogFooter>
                    <Button type="button" variant="outline" onClick={handleCloseForm}>
                      Cancel
                    </Button>
                    <Button type="submit" disabled={saving}>
                      {saving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                      {editingAccount ? 'Update' : 'Add'} Account
                    </Button>
                  </DialogFooter>
                </form>
              </Form>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        {bankAccounts.length === 0 ? (
          <div className="text-center py-8">
            <CreditCard className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Bank Accounts</h3>
            <p className="text-muted-foreground mb-4">
              Add bank accounts to display payment information on invoices
            </p>
            <Button onClick={() => setShowAddForm(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Add Your First Bank Account
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {bankAccounts.map((account) => (
              <div key={account.id} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <h4 className="font-semibold">{account.bank_name}</h4>
                    {account.is_primary && (
                      <Badge variant="default">Primary</Badge>
                    )}
                    <Badge variant="outline">{account.currency}</Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEdit(account)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDelete(account.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-muted-foreground">Account Name</p>
                    <p className="font-medium">{account.account_name}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Account Number</p>
                    <p className="font-medium">{account.account_number}</p>
                  </div>
                  {account.swift_code && (
                    <div>
                      <p className="text-muted-foreground">SWIFT Code</p>
                      <p className="font-medium">{account.swift_code}</p>
                    </div>
                  )}
                  {account.branch && (
                    <div>
                      <p className="text-muted-foreground">Branch</p>
                      <p className="font-medium">{account.branch}</p>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
