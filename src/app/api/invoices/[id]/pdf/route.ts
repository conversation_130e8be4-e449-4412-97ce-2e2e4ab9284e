import { NextRequest, NextResponse } from 'next/server'
import { getInvoiceWithCompanyData } from '@/lib/api/company-settings'
import { InvoicePDFGenerator } from '@/lib/pdf/invoice-pdf-generator'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const invoiceId = params.id

    // Get invoice data with company information
    const data = await getInvoiceWithCompanyData(invoiceId)
    
    if (!data.invoice) {
      return NextResponse.json(
        { error: 'Invoice not found' },
        { status: 404 }
      )
    }

    // For now, return HTML preview until Puppeteer is installed
    const html = InvoicePDFGenerator.generateInvoiceHTML({
      invoice: data.invoice,
      company: data.company,
      template: data.template,
      bankAccounts: data.bankAccounts
    })

    return new NextResponse(html, {
      headers: {
        'Content-Type': 'text/html',
      },
    })

    // TODO: Uncomment when P<PERSON>peteer is installed
    /*
    // Generate PDF
    const pdfBuffer = await InvoicePDFGenerator.generatePDF({
      invoice: data.invoice,
      company: data.company,
      template: data.template,
      bankAccounts: data.bankAccounts
    })

    // Return PDF response
    return new NextResponse(pdfBuffer, {
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="invoice-${data.invoice.invoice_number}.pdf"`,
      },
    })
    */
  } catch (error) {
    console.error('PDF generation error:', error)
    return NextResponse.json(
      { error: 'Failed to generate PDF' },
      { status: 500 }
    )
  }
}
