import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON>Header } from "@/components/layout/page-header"
import { CompanySettingsForm } from "@/components/settings/company-settings-form"

export default function SettingsPage() {
  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <PageHeader
        title="Settings"
        description="Manage your company settings and invoice preferences"
      />

      <div className="space-y-6">
        {/* Company Settings */}
        <CompanySettingsForm />

        {/* Additional Settings Placeholder */}
        <Card>
          <CardHeader>
            <CardTitle>Additional Settings</CardTitle>
            <CardDescription>
              More configuration options will be available here
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8">
              <p className="text-muted-foreground">
                User preferences, notifications, and other settings coming soon
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
